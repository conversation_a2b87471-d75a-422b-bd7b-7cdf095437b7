import java.util.UUID;

class TestQARequest {

public static void main(String[] args) {
            String apiKey = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A==";
        String user = "shs-xxaqbzylhy";
        String uuid = UUID.fastUUID().toString();
        String apiId  = "1745223746865";
        String touchId = "1745223746865";
        testLogin(apiKey, user, uuid,  apiId );

}

private static void testLogin(String  apiKey, String user, String uuid, String appid) {


        String day = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String data = "{\"user\":\""+user+"\", \"name\":\"许国杰\n\", \"orgCode\":\"数据处室\", \"orgName\":\"数据处室\", \"uuid\":\""+uuid+"\", \"day\":\""+day+"\"}";
        String encryptByPublicKey = SM2Utils.encryptByPublicKey(data, apiKey);

        String url = String.format("%s/touchSso/ssoLogin?touchId=%d&params=%s",
                "http://10.8.252.60:3900/api-gateway", Long.valueOf(appid) , encryptByPublicKey);
        RestTemplate restTemplate = new RestTemplate();

        ResponseEntity<LoginResponse> response = restTemplate.getForEntity(url, LoginResponse.class);
        // 打印response
        System.out.println(response.getBody());
    }

}