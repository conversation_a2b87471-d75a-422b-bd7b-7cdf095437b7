from dataclasses import dataclass
from typing import Optional
import json

@dataclass
class LoginResult:
    """登录结果数据类"""
    sessionRenewal: Optional[bool] = None
    token: Optional[str] = None
    username: Optional[str] = None
    realname: Optional[str] = None

@dataclass
class LoginResponse:
    """智能问答登录响应数据类"""
    success: Optional[bool] = None
    message: Optional[str] = None
    code: Optional[int] = None
    result: Optional[LoginResult] = None
    timestamp: Optional[int] = None

    @classmethod
    def from_dict(cls, data: dict) -> 'LoginResponse':
        """从字典创建LoginResponse对象"""
        result_data = data.get('result', {})
        result = LoginResult(
            sessionRenewal=result_data.get('sessionRenewal'),
            token=result_data.get('token'),
            username=result_data.get('username'),
            realname=result_data.get('realname')
        ) if result_data else None

        return cls(
            success=data.get('success'),
            message=data.get('message'),
            code=data.get('code'),
            result=result,
            timestamp=data.get('timestamp')
        )

    def to_formatted_string(self) -> str:
        """格式化输出响应信息"""
        if not self.success:
            return f"登录失败: {self.message} (代码: {self.code})"

        result_info = ""
        if self.result:
            result_info = f"""
登录结果:
  - 会话更新: {self.result.sessionRenewal}
  - 令牌: {self.result.token[:50]}... (已截断)
  - 用户名: {self.result.username}
  - 真实姓名: {self.result.realname}"""

        return f"""登录成功!
消息: {self.message}
状态码: {self.code}
时间戳: {self.timestamp}{result_info}"""

@dataclass
class SessionResponse:
    """会话创建响应数据类"""
    success: Optional[bool] = None
    message: Optional[str] = None
    code: Optional[int] = None
    result: Optional[int] = None  # sessionId
    timestamp: Optional[int] = None

    @classmethod
    def from_dict(cls, data: dict) -> 'SessionResponse':
        """从字典创建SessionResponse对象"""
        return cls(
            success=data.get('success'),
            message=data.get('message'),
            code=data.get('code'),
            result=data.get('result'),  # This is the sessionId
            timestamp=data.get('timestamp')
        )

    def to_formatted_string(self) -> str:
        """格式化输出会话响应信息"""
        if not self.success:
            return f"会话创建失败: {self.message} (代码: {self.code})"

        return f"""会话创建成功!
消息: {self.message}
状态码: {self.code}
会话ID: {self.result}
时间戳: {self.timestamp}"""
