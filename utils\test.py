from gmssl import sm2
from utils.sm2_utils import SM2Utils
import base64
def encrypt_c1c3c222(data: bytes, public_key_hex: str) -> bytes:
    """
    使用 SM2 公钥加密数据，返回 C1C3C2 格式密文
    这是一个修复后的版本，使用正确的 gmssl API
    """
    # 直接使用 gmssl 库的标准加密方法，它已经实现了 C1C3C2 格式
    sm2_cipher = sm2.CryptSM2(public_key=public_key_hex, private_key="")

    # gmssl 的 encrypt 方法已经返回 C1C3C2 格式的密文
    encrypted_data = sm2_cipher.encrypt(data)

    if encrypted_data is None:
        raise ValueError("Encryption failed")

    return encrypted_data





def encrypt_data(data: bytes, public_key_hex: str) -> bytes:
    """
    使用 SM2 公钥加密数据，并返回 C1C3C2 格式密文（与 Hutool 一致）
    """
    # 使用 gmssl 库的标准加密方法
    sm2_cipher = sm2.CryptSM2(public_key=public_key_hex, private_key="")

    # 直接使用 gmssl 的加密功能
    encrypted_data = sm2_cipher.encrypt(data)

    if encrypted_data is None:
        raise ValueError("Encryption failed")

    return encrypted_data
def encrypt_c1c3c2(cipher_c1c2c3) -> str:

    # Step 2: 手动解析 C1C2C3 结构
    c1 = cipher_c1c2c3[:65]         # C1 是椭圆曲线点（65 字节）
    c3 = cipher_c1c2c3[-32:]        # C3 是摘要（32 字节）
    c2 = cipher_c1c2c3[65:-32]      # C2 是数据密文

    # Step 3: 拼接为 C1C3C2 格式
    cipher_c1c3c2 = c1 + c3 + c2

    # 返回 Hex 编码（大写）
    return cipher_c1c3c2.hex().upper()


sm2Utils =  SM2Utils()

publickey = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=="

data = "Hello from Java"

der_data = base64.b64decode(publickey.strip())
print(der_data)
extractedPublicKey = sm2Utils.extract_raw_pubkey_from_x509_der(der_data)

####  from java Public Key    EC Public Key [a5:d4:f7:89:4d:c4:81:f8:03:14:e3:a7:95:3b:47:8b:6c:22:2d:88]
#          X: bae7ade4862db210be1715c09d380218f6d4d32ca8cdbf50355c2a2d87a31916
#         Y: 1cca45626608499c7deace9d20d22ade7cb6f255ba0b3b97520b17ad99f72dc

print("the extracted Public Key is: " +  extractedPublicKey)

print(sm2Utils.encrypt_by_public_key(data, publickey))

# print(sm2Utils.encrypt_by_public_key(data, publickey))


## 0404C27E487949AAB22DD6BF5D1A26AC2BA31AE6EC764A9171382825F3BFEB26F2B821FCB3FA9BBF025389F4A6861C099DD0AADC6B95987D5FC64D3A9BF25015A92C6DBB5615DBD7F655B48BF63209DE6FC43561B496A4AD51F86712528D21175132EE4B14B71B24E02D20AA350D1A56
## 04D98BE63D508E40275AB7A41405D56BEEEE7148CEFC99DBB46A2C8A7ED3358B7F136F022B80049C0A0B2EA53B1B6193D51256C533E0B631DEF77049BA95E58DF549F5256A8B1D6DFF19FF880319F1841D500E0177732F7A4F9749DCC0FAF7208715AA42D39729F031DC06059D45437F