#!/usr/bin/env python3
"""
测试snowland-smx的输出格式，分析与Java格式的差异
"""
from pysmx.SM2 import Encrypt, generate_keypair
import base64

def test_snowland_format():
    """测试snowland-smx的输出格式"""
    print("=== 测试snowland-smx输出格式 ===")
    
    # 生成测试密钥对
    pk, sk = generate_keypair()
    print(f"生成的公钥: {pk.hex()}")
    print(f"公钥长度: {len(pk)} bytes")
    print(f"公钥开头: {pk.hex()[:10]}")
    
    # 测试数据
    data = b'test'
    print(f"\n测试数据: {data}")
    
    # 测试不同参数
    print("\n=== 测试不同参数 ===")
    
    # len_para=64, is_hex=0
    result1 = Encrypt(data, pk, 64, 0)
    if result1:
        hex1 = result1.hex().upper()
        print(f"len_para=64, is_hex=0:")
        print(f"  长度: {len(result1)} bytes ({len(hex1)} hex chars)")
        print(f"  开头: {hex1[:10]}")
        print(f"  完整: {hex1}")
    
    # len_para=32, is_hex=0
    result2 = Encrypt(data, pk, 32, 0)
    if result2:
        hex2 = result2.hex().upper()
        print(f"\nlen_para=32, is_hex=0:")
        print(f"  长度: {len(result2)} bytes ({len(hex2)} hex chars)")
        print(f"  开头: {hex2[:10]}")
        print(f"  完整: {hex2}")
    
    return result1, result2

def analyze_format(encrypted_data):
    """分析加密数据的格式"""
    if not encrypted_data:
        return
        
    print(f"\n=== 分析加密数据格式 ===")
    hex_data = encrypted_data.hex().upper()
    print(f"总长度: {len(encrypted_data)} bytes ({len(hex_data)} hex chars)")
    
    # SM2加密结果通常包含 C1(椭圆曲线点) + C3(摘要) + C2(密文)
    # 或者 C1 + C2 + C3 格式
    
    # 检查是否以04开头（未压缩点格式）
    if hex_data.startswith('04'):
        print("✅ 以04开头 - 未压缩椭圆曲线点格式")
        # C1部分应该是65字节（04 + 32字节x坐标 + 32字节y坐标）
        c1_part = hex_data[:130]  # 65 bytes = 130 hex chars
        print(f"C1部分 (65 bytes): {c1_part}")
        
        remaining = hex_data[130:]
        print(f"剩余部分长度: {len(remaining)//2} bytes")
        print(f"剩余部分: {remaining}")
        
    else:
        print("❌ 不以04开头")
        # 可能是压缩格式或其他格式
        print(f"开头字节: {hex_data[:10]}")
        
        # 尝试分析可能的C1部分（33字节压缩格式）
        if hex_data.startswith(('02', '03')):
            print("可能是压缩椭圆曲线点格式")
            c1_part = hex_data[:66]  # 33 bytes = 66 hex chars
            print(f"C1部分 (33 bytes): {c1_part}")
            
            remaining = hex_data[66:]
            print(f"剩余部分长度: {len(remaining)//2} bytes")
            print(f"剩余部分: {remaining}")

def test_with_04_prefix():
    """测试添加04前缀是否能匹配Java格式"""
    print("\n=== 测试04前缀格式 ===")
    
    # 生成测试密钥对
    pk, sk = generate_keypair()
    
    # 为公钥添加04前缀（如果还没有）
    pk_hex = pk.hex()
    if not pk_hex.startswith('04'):
        pk_with_04 = bytes.fromhex('04' + pk_hex)
        print(f"原始公钥: {pk_hex}")
        print(f"添加04前缀: {pk_with_04.hex()}")
        
        # 测试加密
        try:
            result = Encrypt(b'test', pk_with_04, 64, 0)
            if result:
                hex_result = result.hex().upper()
                print(f"加密成功:")
                print(f"  长度: {len(result)} bytes")
                print(f"  开头: {hex_result[:10]}")
                print(f"  是否以04开头: {hex_result.startswith('04')}")
                return result
        except Exception as e:
            print(f"加密失败: {e}")
    
    return None

def main():
    """主函数"""
    print("🔍 snowland-smx格式分析")
    print("=" * 50)
    
    # 测试基本格式
    result1, result2 = test_snowland_format()
    
    # 分析第一个结果
    if result1:
        analyze_format(result1)
    
    # 测试04前缀
    result_04 = test_with_04_prefix()
    if result_04:
        analyze_format(result_04)
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 分析总结:")
    print("1. snowland-smx生成的加密结果格式需要进一步分析")
    print("2. Java结果以04开头，表示未压缩椭圆曲线点")
    print("3. 需要调整snowland-smx的参数或后处理来匹配Java格式")

if __name__ == "__main__":
    main()
