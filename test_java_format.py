#!/usr/bin/env python3
"""
测试snowland-smx与Java格式的兼容性
"""
from utils.sm2_snowland import SM2UtilsSnowland

def test_java_format():
    """测试与Java格式的兼容性"""
    print("=== 测试snowland-smx与Java格式兼容性 ===")
    
    public_key = 'MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=='
    
    print("测试多次加密，验证格式一致性...")
    
    for i in range(5):
        result = SM2UtilsSnowland.encrypt_by_public_key('test', public_key)
        if result:
            starts_with_04 = result.startswith('04')
            print(f"测试 {i+1}: {result[:20]}... (长度: {len(result)}, 以04开头: {starts_with_04})")
            
            if not starts_with_04:
                print(f"❌ 错误：结果不以04开头！")
                return False
        else:
            print(f"❌ 测试 {i+1} 加密失败")
            return False
    
    print("\n✅ 所有测试都以04开头，符合Java格式要求！")
    
    # 测试不同长度的数据
    print("\n测试不同长度的数据...")
    test_data = [
        "a",
        "test",
        "Hello World",
        "这是中文测试数据",
        '{"user":"test", "data":"long test data for encryption"}'
    ]
    
    for data in test_data:
        result = SM2UtilsSnowland.encrypt_by_public_key(data, public_key)
        if result:
            starts_with_04 = result.startswith('04')
            print(f"数据: '{data[:20]}...' -> 长度: {len(result)}, 以04开头: {starts_with_04}")
            
            if not starts_with_04:
                print(f"❌ 错误：结果不以04开头！")
                return False
        else:
            print(f"❌ 数据 '{data}' 加密失败")
            return False
    
    print("\n🎉 所有测试通过！snowland-smx现在与Java格式兼容")
    return True

if __name__ == "__main__":
    test_java_format()
